# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.17

CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: ../chart.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: SerialPlot_autogen/EWIEGA46WW/moc_chart.cpp
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: SerialPlot_autogen/EWIEGA46WW/moc_configuration.cpp
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: SerialPlot_autogen/EWIEGA46WW/moc_decoderbase.cpp
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: SerialPlot_autogen/EWIEGA46WW/moc_decoderbin.cpp
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: SerialPlot_autogen/EWIEGA46WW/moc_decodercsv.cpp
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: SerialPlot_autogen/EWIEGA46WW/moc_decoderhdlc.cpp
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: SerialPlot_autogen/EWIEGA46WW/moc_decoderplugin.cpp
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: SerialPlot_autogen/EWIEGA46WW/moc_displaybase.cpp
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: SerialPlot_autogen/EWIEGA46WW/moc_mainwindow.cpp
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: SerialPlot_autogen/EWIEGA46WW/moc_plugin.cpp
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: SerialPlot_autogen/EWIEGA46WW/moc_portbase.cpp
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: SerialPlot_autogen/EWIEGA46WW/moc_porthid.cpp
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: SerialPlot_autogen/EWIEGA46WW/moc_portrs232.cpp
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: SerialPlot_autogen/EWIEGA46WW/moc_serialchartjs.cpp
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: SerialPlot_autogen/mocs_compilation.cpp
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: ../common.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: ../configuration.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: ../decoderbase.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: ../decoderbin.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: ../decodercsv.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: ../decoderhdlc.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: ../decoderplugin.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: ../displaybase.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: ../hiddevice.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: ../mainwindow.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: ../plugin.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: ../portbase.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: ../porthid.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: ../portrs232.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: ../serialchartjs.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QDebug
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QFlags
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMetaType
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSharedDataPointer
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcryptographichash.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatetime.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslConfiguration
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslPreSharedKeyAuthenticator
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qabstractsocket.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qnetworkaccessmanager.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qssl.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslcertificate.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslconfiguration.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslerror.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslpresharedkeyauthenticator.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslsocket.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qtcpsocket.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebkitglobal.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebsettings.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/QWebView
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebpage.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebview.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QWidget
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h

CMakeFiles/SerialPlot.dir/chart.cpp.obj: ../chart.cpp
CMakeFiles/SerialPlot.dir/chart.cpp.obj: ../chart.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: ../common.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: ../configuration.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: ../decoderbase.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QWidget
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h

CMakeFiles/SerialPlot.dir/configuration.cpp.obj: ../common.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: ../configuration.cpp
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: ../configuration.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h

CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: ../common.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: ../configuration.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: ../decoderbase.cpp
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: ../decoderbase.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: ../decoderbin.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: ../decodercsv.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: ../decoderhdlc.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: ../decoderplugin.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: ../displaybase.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: ../mainwindow.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: ../portbase.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h

CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: ../common.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: ../configuration.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: ../decoderbase.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: ../decoderbin.cpp
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: ../decoderbin.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h

CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: ../common.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: ../configuration.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: ../decoderbase.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: ../decodercsv.cpp
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: ../decodercsv.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h

CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: ../common.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: ../configuration.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: ../decoderbase.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: ../decoderbin.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: ../decoderhdlc.cpp
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: ../decoderhdlc.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h

CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: ../common.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: ../configuration.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: ../decoderbase.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: ../decoderplugin.cpp
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: ../decoderplugin.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: ../displaybase.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: ../hiddevice.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: ../mainwindow.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: ../plugin.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: ../portbase.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QDebug
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QFlags
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMetaType
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSharedDataPointer
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcryptographichash.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatetime.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslConfiguration
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslPreSharedKeyAuthenticator
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qabstractsocket.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qnetworkaccessmanager.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qssl.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslcertificate.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslconfiguration.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslerror.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslpresharedkeyauthenticator.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslsocket.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qtcpsocket.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebkitglobal.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebsettings.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/QWebView
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebpage.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebview.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h

CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: ../common.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: ../configuration.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: ../decoderbase.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: ../displaybase.cpp
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: ../displaybase.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h

CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: ../hiddevice.cpp
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: ../hiddevice.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h

CMakeFiles/SerialPlot.dir/main.cpp.obj: ../chart.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: ../common.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: ../configuration.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: ../decoderbase.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: ../displaybase.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: ../main.cpp
CMakeFiles/SerialPlot.dir/main.cpp.obj: ../mainwindow.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: ../portbase.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QFlags
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMetaType
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSharedDataPointer
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QVariant
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qabstractitemmodel.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcryptographichash.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatetime.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qitemselectionmodel.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregularexpression.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qabstracttextdocumentlayout.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontdatabase.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qglyphrun.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qguiapplication.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qinputmethod.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrawfont.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextcursor.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextdocument.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextformat.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextlayout.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvalidator.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslConfiguration
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslPreSharedKeyAuthenticator
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qabstractsocket.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qnetworkaccessmanager.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qssl.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslcertificate.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslconfiguration.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslerror.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslpresharedkeyauthenticator.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslsocket.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qtcpsocket.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebkitglobal.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebsettings.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/QWebView
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebpage.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebview.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QAction
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QApplication
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QButtonGroup
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QCheckBox
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QDockWidget
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFormLayout
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFrame
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QHBoxLayout
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QHeaderView
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QLabel
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QLayout
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QLineEdit
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMenu
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMenuBar
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QPlainTextEdit
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QPushButton
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QScrollArea
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QSpacerItem
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QStatusBar
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QToolBar
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QVBoxLayout
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QWidget
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractbutton.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractitemdelegate.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractitemview.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractscrollarea.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractslider.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractspinbox.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qaction.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qactiongroup.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qapplication.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qboxlayout.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qbuttongroup.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qcheckbox.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdesktopwidget.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdockwidget.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qformlayout.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qframe.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qgridlayout.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qheaderview.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qlabel.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qlayout.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qlayoutitem.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qlineedit.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmenu.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmenubar.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qplaintextedit.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qpushbutton.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qrubberband.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qscrollarea.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qslider.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qstatusbar.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qstyle.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qstyleoption.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabbar.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtextedit.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtoolbar.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
CMakeFiles/SerialPlot.dir/main.cpp.obj: SerialPlot_autogen/include/ui_mainwindow.h

CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: ../chart.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: ../common.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: ../configuration.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: ../decoderbase.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: ../decoderplugin.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: ../displaybase.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: ../hiddevice.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: ../mainwindow.cpp
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: ../mainwindow.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: ../plugin.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: ../portbase.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QDebug
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QFlags
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMetaType
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSharedDataPointer
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QVariant
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qabstractitemmodel.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcryptographichash.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatetime.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qitemselectionmodel.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregularexpression.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qabstracttextdocumentlayout.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontdatabase.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qglyphrun.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qguiapplication.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qinputmethod.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrawfont.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextcursor.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextdocument.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextformat.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextlayout.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvalidator.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslConfiguration
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslPreSharedKeyAuthenticator
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qabstractsocket.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qnetworkaccessmanager.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qssl.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslcertificate.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslconfiguration.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslerror.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslpresharedkeyauthenticator.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslsocket.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qtcpsocket.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebkitglobal.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebsettings.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/QWebFrame
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/QWebView
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebframe.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebpage.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebview.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QAction
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QApplication
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QButtonGroup
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QCheckBox
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QDockWidget
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFormLayout
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFrame
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QHBoxLayout
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QHeaderView
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QLabel
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QLayout
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QLineEdit
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMenu
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMenuBar
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QPlainTextEdit
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QPushButton
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QScrollArea
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QSpacerItem
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QStatusBar
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QToolBar
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QVBoxLayout
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QWidget
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractbutton.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractitemdelegate.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractitemview.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractscrollarea.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractslider.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractspinbox.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qaction.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qactiongroup.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qapplication.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qboxlayout.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qbuttongroup.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qcheckbox.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdesktopwidget.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdockwidget.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qformlayout.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qframe.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qgridlayout.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qheaderview.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qlabel.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qlayout.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qlayoutitem.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qlineedit.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmenu.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmenubar.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qplaintextedit.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qpushbutton.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qrubberband.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qscrollarea.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qslider.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qstatusbar.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qstyle.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qstyleoption.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabbar.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtextedit.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtoolbar.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: SerialPlot_autogen/include/ui_mainwindow.h

CMakeFiles/SerialPlot.dir/plugin.cpp.obj: ../common.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: ../configuration.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: ../decoderbase.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: ../decoderplugin.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: ../displaybase.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: ../hiddevice.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: ../mainwindow.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: ../plugin.cpp
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: ../plugin.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: ../portbase.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QDebug
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QFlags
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QJsonArray
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QJsonDocument
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMetaType
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSharedDataPointer
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcryptographichash.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatetime.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qjsonarray.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qjsondocument.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qjsonvalue.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslConfiguration
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslPreSharedKeyAuthenticator
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qabstractsocket.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qnetworkaccessmanager.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qssl.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslcertificate.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslconfiguration.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslerror.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslpresharedkeyauthenticator.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslsocket.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qtcpsocket.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebkitglobal.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebsettings.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/QWebView
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebpage.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebview.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QWidget
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h

CMakeFiles/SerialPlot.dir/portbase.cpp.obj: ../common.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: ../configuration.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: ../decoderbase.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: ../hiddevice.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: ../portbase.cpp
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: ../portbase.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: ../porthid.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: ../portrs232.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h

CMakeFiles/SerialPlot.dir/porthid.cpp.obj: ../common.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: ../configuration.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: ../decoderbase.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: ../displaybase.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: ../hiddevice.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: ../mainwindow.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: ../portbase.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: ../porthid.cpp
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: ../porthid.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h

CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: ../common.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: ../configuration.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: ../decoderbase.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: ../displaybase.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: ../mainwindow.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: ../portbase.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: ../portrs232.cpp
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: ../portrs232.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QDebug
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h

CMakeFiles/SerialPlot.dir/qrc_resources.cpp.obj: qrc_resources.cpp

CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: ../common.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: ../configuration.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: ../decoderbase.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: ../displaybase.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: ../hiddevice.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: ../mainwindow.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: ../portbase.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: ../serialchartjs.cpp
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: ../serialchartjs.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QDebug
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QFlags
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMetaType
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSharedDataPointer
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcryptographichash.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatetime.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslConfiguration
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslPreSharedKeyAuthenticator
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qabstractsocket.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qnetworkaccessmanager.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qssl.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslcertificate.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslconfiguration.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslerror.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslpresharedkeyauthenticator.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslsocket.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qtcpsocket.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebkitglobal.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebsettings.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/QWebView
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebpage.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebview.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QWidget
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h

