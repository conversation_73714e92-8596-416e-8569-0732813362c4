file(REMOVE_RECURSE
  "CMakeFiles/SerialChart_autogen.dir/AutogenUsed.txt"
  "CMakeFiles/SerialChart_autogen.dir/ParseCache.txt"
  "SerialChart_autogen"
  "CMakeFiles/SerialChart.dir/SerialChart_autogen/mocs_compilation.cpp.obj"
  "CMakeFiles/SerialChart.dir/chart.cpp.obj"
  "CMakeFiles/SerialChart.dir/configuration.cpp.obj"
  "CMakeFiles/SerialChart.dir/decoderbase.cpp.obj"
  "CMakeFiles/SerialChart.dir/decoderbin.cpp.obj"
  "CMakeFiles/SerialChart.dir/decodercsv.cpp.obj"
  "CMakeFiles/SerialChart.dir/decoderhdlc.cpp.obj"
  "CMakeFiles/SerialChart.dir/decoderplugin.cpp.obj"
  "CMakeFiles/SerialChart.dir/displaybase.cpp.obj"
  "CMakeFiles/SerialChart.dir/hiddevice.cpp.obj"
  "CMakeFiles/SerialChart.dir/main.cpp.obj"
  "CMakeFiles/SerialChart.dir/mainwindow.cpp.obj"
  "CMakeFiles/SerialChart.dir/plugin.cpp.obj"
  "CMakeFiles/SerialChart.dir/portbase.cpp.obj"
  "CMakeFiles/SerialChart.dir/porthid.cpp.obj"
  "CMakeFiles/SerialChart.dir/portrs232.cpp.obj"
  "CMakeFiles/SerialChart.dir/qrc_resources.cpp.obj"
  "CMakeFiles/SerialChart.dir/serialchartjs.cpp.obj"
  "SerialChart.exe"
  "SerialChart.exe.manifest"
  "SerialChart.pdb"
  "libSerialChart.dll.a"
  "qrc_resources.cpp"
)

# Per-language clean rules from dependency scanning.
foreach(lang CXX)
  include(CMakeFiles/SerialChart.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
