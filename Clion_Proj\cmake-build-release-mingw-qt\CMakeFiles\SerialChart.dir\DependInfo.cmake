# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/SerialChart_autogen/mocs_compilation.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/SerialChart_autogen/mocs_compilation.cpp.obj"
  "C:/Users/<USER>/Desktop/SerialChart/chart.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/chart.cpp.obj"
  "C:/Users/<USER>/Desktop/SerialChart/configuration.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/configuration.cpp.obj"
  "C:/Users/<USER>/Desktop/SerialChart/decoderbase.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/decoderbase.cpp.obj"
  "C:/Users/<USER>/Desktop/SerialChart/decoderbin.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/decoderbin.cpp.obj"
  "C:/Users/<USER>/Desktop/SerialChart/decodercsv.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/decodercsv.cpp.obj"
  "C:/Users/<USER>/Desktop/SerialChart/decoderhdlc.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/decoderhdlc.cpp.obj"
  "C:/Users/<USER>/Desktop/SerialChart/decoderplugin.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/decoderplugin.cpp.obj"
  "C:/Users/<USER>/Desktop/SerialChart/displaybase.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/displaybase.cpp.obj"
  "C:/Users/<USER>/Desktop/SerialChart/hiddevice.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/hiddevice.cpp.obj"
  "C:/Users/<USER>/Desktop/SerialChart/main.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/main.cpp.obj"
  "C:/Users/<USER>/Desktop/SerialChart/mainwindow.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/mainwindow.cpp.obj"
  "C:/Users/<USER>/Desktop/SerialChart/plugin.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/plugin.cpp.obj"
  "C:/Users/<USER>/Desktop/SerialChart/portbase.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/portbase.cpp.obj"
  "C:/Users/<USER>/Desktop/SerialChart/porthid.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/porthid.cpp.obj"
  "C:/Users/<USER>/Desktop/SerialChart/portrs232.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/portrs232.cpp.obj"
  "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/qrc_resources.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/qrc_resources.cpp.obj"
  "C:/Users/<USER>/Desktop/SerialChart/serialchartjs.cpp" "C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/CMakeFiles/SerialChart.dir/serialchartjs.cpp.obj"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "QT_CORE_LIB"
  "QT_GUI_LIB"
  "QT_NETWORK_LIB"
  "QT_NO_DEBUG"
  "QT_SERIALPORT_LIB"
  "QT_WEBKITWIDGETS_LIB"
  "QT_WEBKIT_LIB"
  "QT_WIDGETS_LIB"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "SerialChart_autogen/include"
  "../"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/./mkspecs/win32-g++"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
