file(REMOVE_RECURSE
  "CMakeFiles/SerialPlot_autogen.dir/AutogenUsed.txt"
  "CMakeFiles/SerialPlot_autogen.dir/ParseCache.txt"
  "SerialPlot_autogen"
  "CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj"
  "CMakeFiles/SerialPlot.dir/chart.cpp.obj"
  "CMakeFiles/SerialPlot.dir/configuration.cpp.obj"
  "CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj"
  "CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj"
  "CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj"
  "CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj"
  "CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj"
  "CMakeFiles/SerialPlot.dir/displaybase.cpp.obj"
  "CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj"
  "CMakeFiles/SerialPlot.dir/main.cpp.obj"
  "CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj"
  "CMakeFiles/SerialPlot.dir/plugin.cpp.obj"
  "CMakeFiles/SerialPlot.dir/portbase.cpp.obj"
  "CMakeFiles/SerialPlot.dir/porthid.cpp.obj"
  "CMakeFiles/SerialPlot.dir/portrs232.cpp.obj"
  "CMakeFiles/SerialPlot.dir/qrc_resources.cpp.obj"
  "CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj"
  "SerialPlot.exe"
  "SerialPlot.exe.manifest"
  "SerialPlot.pdb"
  "libSerialPlot.dll.a"
  "qrc_resources.cpp"
)

# Per-language clean rules from dependency scanning.
foreach(lang CXX)
  include(CMakeFiles/SerialPlot.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
