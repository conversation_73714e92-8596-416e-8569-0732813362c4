"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E rm -f CMakeFiles\SerialPlot.dir/objects.a
D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\ar.exe cr CMakeFiles\SerialPlot.dir/objects.a @CMakeFiles\SerialPlot.dir\objects1.rsp
D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe -O3 -DNDEBUG   -Wl,--whole-archive CMakeFiles\SerialPlot.dir/objects.a -Wl,--no-whole-archive  -o SerialPlot.exe -Wl,--out-implib,libSerialPlot.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\SerialPlot.dir\linklibs.rsp
