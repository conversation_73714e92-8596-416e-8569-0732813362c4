# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.17

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe"

# The command to remove a file.
RM = "D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Desktop\SerialChart

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E echo "No interactive CMake dialog available."
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named SerialChart

# Build rule for target.
SerialChart: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 SerialChart
.PHONY : SerialChart

# fast build rule for target.
SerialChart/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/build
.PHONY : SerialChart/fast

#=============================================================================
# Target rules for targets named SerialChart_autogen

# Build rule for target.
SerialChart_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 SerialChart_autogen
.PHONY : SerialChart_autogen

# fast build rule for target.
SerialChart_autogen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart_autogen.dir\build.make CMakeFiles/SerialChart_autogen.dir/build
.PHONY : SerialChart_autogen/fast

SerialChart_autogen/mocs_compilation.obj: SerialChart_autogen/mocs_compilation.cpp.obj

.PHONY : SerialChart_autogen/mocs_compilation.obj

# target to build an object file
SerialChart_autogen/mocs_compilation.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/SerialChart_autogen/mocs_compilation.cpp.obj
.PHONY : SerialChart_autogen/mocs_compilation.cpp.obj

SerialChart_autogen/mocs_compilation.i: SerialChart_autogen/mocs_compilation.cpp.i

.PHONY : SerialChart_autogen/mocs_compilation.i

# target to preprocess a source file
SerialChart_autogen/mocs_compilation.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/SerialChart_autogen/mocs_compilation.cpp.i
.PHONY : SerialChart_autogen/mocs_compilation.cpp.i

SerialChart_autogen/mocs_compilation.s: SerialChart_autogen/mocs_compilation.cpp.s

.PHONY : SerialChart_autogen/mocs_compilation.s

# target to generate assembly for a file
SerialChart_autogen/mocs_compilation.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/SerialChart_autogen/mocs_compilation.cpp.s
.PHONY : SerialChart_autogen/mocs_compilation.cpp.s

chart.obj: chart.cpp.obj

.PHONY : chart.obj

# target to build an object file
chart.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/chart.cpp.obj
.PHONY : chart.cpp.obj

chart.i: chart.cpp.i

.PHONY : chart.i

# target to preprocess a source file
chart.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/chart.cpp.i
.PHONY : chart.cpp.i

chart.s: chart.cpp.s

.PHONY : chart.s

# target to generate assembly for a file
chart.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/chart.cpp.s
.PHONY : chart.cpp.s

configuration.obj: configuration.cpp.obj

.PHONY : configuration.obj

# target to build an object file
configuration.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/configuration.cpp.obj
.PHONY : configuration.cpp.obj

configuration.i: configuration.cpp.i

.PHONY : configuration.i

# target to preprocess a source file
configuration.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/configuration.cpp.i
.PHONY : configuration.cpp.i

configuration.s: configuration.cpp.s

.PHONY : configuration.s

# target to generate assembly for a file
configuration.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/configuration.cpp.s
.PHONY : configuration.cpp.s

decoderbase.obj: decoderbase.cpp.obj

.PHONY : decoderbase.obj

# target to build an object file
decoderbase.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/decoderbase.cpp.obj
.PHONY : decoderbase.cpp.obj

decoderbase.i: decoderbase.cpp.i

.PHONY : decoderbase.i

# target to preprocess a source file
decoderbase.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/decoderbase.cpp.i
.PHONY : decoderbase.cpp.i

decoderbase.s: decoderbase.cpp.s

.PHONY : decoderbase.s

# target to generate assembly for a file
decoderbase.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/decoderbase.cpp.s
.PHONY : decoderbase.cpp.s

decoderbin.obj: decoderbin.cpp.obj

.PHONY : decoderbin.obj

# target to build an object file
decoderbin.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/decoderbin.cpp.obj
.PHONY : decoderbin.cpp.obj

decoderbin.i: decoderbin.cpp.i

.PHONY : decoderbin.i

# target to preprocess a source file
decoderbin.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/decoderbin.cpp.i
.PHONY : decoderbin.cpp.i

decoderbin.s: decoderbin.cpp.s

.PHONY : decoderbin.s

# target to generate assembly for a file
decoderbin.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/decoderbin.cpp.s
.PHONY : decoderbin.cpp.s

decodercsv.obj: decodercsv.cpp.obj

.PHONY : decodercsv.obj

# target to build an object file
decodercsv.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/decodercsv.cpp.obj
.PHONY : decodercsv.cpp.obj

decodercsv.i: decodercsv.cpp.i

.PHONY : decodercsv.i

# target to preprocess a source file
decodercsv.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/decodercsv.cpp.i
.PHONY : decodercsv.cpp.i

decodercsv.s: decodercsv.cpp.s

.PHONY : decodercsv.s

# target to generate assembly for a file
decodercsv.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/decodercsv.cpp.s
.PHONY : decodercsv.cpp.s

decoderhdlc.obj: decoderhdlc.cpp.obj

.PHONY : decoderhdlc.obj

# target to build an object file
decoderhdlc.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/decoderhdlc.cpp.obj
.PHONY : decoderhdlc.cpp.obj

decoderhdlc.i: decoderhdlc.cpp.i

.PHONY : decoderhdlc.i

# target to preprocess a source file
decoderhdlc.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/decoderhdlc.cpp.i
.PHONY : decoderhdlc.cpp.i

decoderhdlc.s: decoderhdlc.cpp.s

.PHONY : decoderhdlc.s

# target to generate assembly for a file
decoderhdlc.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/decoderhdlc.cpp.s
.PHONY : decoderhdlc.cpp.s

decoderplugin.obj: decoderplugin.cpp.obj

.PHONY : decoderplugin.obj

# target to build an object file
decoderplugin.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/decoderplugin.cpp.obj
.PHONY : decoderplugin.cpp.obj

decoderplugin.i: decoderplugin.cpp.i

.PHONY : decoderplugin.i

# target to preprocess a source file
decoderplugin.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/decoderplugin.cpp.i
.PHONY : decoderplugin.cpp.i

decoderplugin.s: decoderplugin.cpp.s

.PHONY : decoderplugin.s

# target to generate assembly for a file
decoderplugin.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/decoderplugin.cpp.s
.PHONY : decoderplugin.cpp.s

displaybase.obj: displaybase.cpp.obj

.PHONY : displaybase.obj

# target to build an object file
displaybase.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/displaybase.cpp.obj
.PHONY : displaybase.cpp.obj

displaybase.i: displaybase.cpp.i

.PHONY : displaybase.i

# target to preprocess a source file
displaybase.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/displaybase.cpp.i
.PHONY : displaybase.cpp.i

displaybase.s: displaybase.cpp.s

.PHONY : displaybase.s

# target to generate assembly for a file
displaybase.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/displaybase.cpp.s
.PHONY : displaybase.cpp.s

hiddevice.obj: hiddevice.cpp.obj

.PHONY : hiddevice.obj

# target to build an object file
hiddevice.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/hiddevice.cpp.obj
.PHONY : hiddevice.cpp.obj

hiddevice.i: hiddevice.cpp.i

.PHONY : hiddevice.i

# target to preprocess a source file
hiddevice.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/hiddevice.cpp.i
.PHONY : hiddevice.cpp.i

hiddevice.s: hiddevice.cpp.s

.PHONY : hiddevice.s

# target to generate assembly for a file
hiddevice.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/hiddevice.cpp.s
.PHONY : hiddevice.cpp.s

main.obj: main.cpp.obj

.PHONY : main.obj

# target to build an object file
main.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/main.cpp.obj
.PHONY : main.cpp.obj

main.i: main.cpp.i

.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s

.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/main.cpp.s
.PHONY : main.cpp.s

mainwindow.obj: mainwindow.cpp.obj

.PHONY : mainwindow.obj

# target to build an object file
mainwindow.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/mainwindow.cpp.obj
.PHONY : mainwindow.cpp.obj

mainwindow.i: mainwindow.cpp.i

.PHONY : mainwindow.i

# target to preprocess a source file
mainwindow.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/mainwindow.cpp.i
.PHONY : mainwindow.cpp.i

mainwindow.s: mainwindow.cpp.s

.PHONY : mainwindow.s

# target to generate assembly for a file
mainwindow.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/mainwindow.cpp.s
.PHONY : mainwindow.cpp.s

plugin.obj: plugin.cpp.obj

.PHONY : plugin.obj

# target to build an object file
plugin.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/plugin.cpp.obj
.PHONY : plugin.cpp.obj

plugin.i: plugin.cpp.i

.PHONY : plugin.i

# target to preprocess a source file
plugin.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/plugin.cpp.i
.PHONY : plugin.cpp.i

plugin.s: plugin.cpp.s

.PHONY : plugin.s

# target to generate assembly for a file
plugin.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/plugin.cpp.s
.PHONY : plugin.cpp.s

portbase.obj: portbase.cpp.obj

.PHONY : portbase.obj

# target to build an object file
portbase.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/portbase.cpp.obj
.PHONY : portbase.cpp.obj

portbase.i: portbase.cpp.i

.PHONY : portbase.i

# target to preprocess a source file
portbase.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/portbase.cpp.i
.PHONY : portbase.cpp.i

portbase.s: portbase.cpp.s

.PHONY : portbase.s

# target to generate assembly for a file
portbase.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/portbase.cpp.s
.PHONY : portbase.cpp.s

porthid.obj: porthid.cpp.obj

.PHONY : porthid.obj

# target to build an object file
porthid.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/porthid.cpp.obj
.PHONY : porthid.cpp.obj

porthid.i: porthid.cpp.i

.PHONY : porthid.i

# target to preprocess a source file
porthid.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/porthid.cpp.i
.PHONY : porthid.cpp.i

porthid.s: porthid.cpp.s

.PHONY : porthid.s

# target to generate assembly for a file
porthid.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/porthid.cpp.s
.PHONY : porthid.cpp.s

portrs232.obj: portrs232.cpp.obj

.PHONY : portrs232.obj

# target to build an object file
portrs232.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/portrs232.cpp.obj
.PHONY : portrs232.cpp.obj

portrs232.i: portrs232.cpp.i

.PHONY : portrs232.i

# target to preprocess a source file
portrs232.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/portrs232.cpp.i
.PHONY : portrs232.cpp.i

portrs232.s: portrs232.cpp.s

.PHONY : portrs232.s

# target to generate assembly for a file
portrs232.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/portrs232.cpp.s
.PHONY : portrs232.cpp.s

qrc_resources.obj: qrc_resources.cpp.obj

.PHONY : qrc_resources.obj

# target to build an object file
qrc_resources.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/qrc_resources.cpp.obj
.PHONY : qrc_resources.cpp.obj

qrc_resources.i: qrc_resources.cpp.i

.PHONY : qrc_resources.i

# target to preprocess a source file
qrc_resources.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/qrc_resources.cpp.i
.PHONY : qrc_resources.cpp.i

qrc_resources.s: qrc_resources.cpp.s

.PHONY : qrc_resources.s

# target to generate assembly for a file
qrc_resources.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/qrc_resources.cpp.s
.PHONY : qrc_resources.cpp.s

serialchartjs.obj: serialchartjs.cpp.obj

.PHONY : serialchartjs.obj

# target to build an object file
serialchartjs.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/serialchartjs.cpp.obj
.PHONY : serialchartjs.cpp.obj

serialchartjs.i: serialchartjs.cpp.i

.PHONY : serialchartjs.i

# target to preprocess a source file
serialchartjs.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/serialchartjs.cpp.i
.PHONY : serialchartjs.cpp.i

serialchartjs.s: serialchartjs.cpp.s

.PHONY : serialchartjs.s

# target to generate assembly for a file
serialchartjs.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/serialchartjs.cpp.s
.PHONY : serialchartjs.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... SerialChart_autogen
	@echo ... SerialChart
	@echo ... SerialChart_autogen/mocs_compilation.obj
	@echo ... SerialChart_autogen/mocs_compilation.i
	@echo ... SerialChart_autogen/mocs_compilation.s
	@echo ... chart.obj
	@echo ... chart.i
	@echo ... chart.s
	@echo ... configuration.obj
	@echo ... configuration.i
	@echo ... configuration.s
	@echo ... decoderbase.obj
	@echo ... decoderbase.i
	@echo ... decoderbase.s
	@echo ... decoderbin.obj
	@echo ... decoderbin.i
	@echo ... decoderbin.s
	@echo ... decodercsv.obj
	@echo ... decodercsv.i
	@echo ... decodercsv.s
	@echo ... decoderhdlc.obj
	@echo ... decoderhdlc.i
	@echo ... decoderhdlc.s
	@echo ... decoderplugin.obj
	@echo ... decoderplugin.i
	@echo ... decoderplugin.s
	@echo ... displaybase.obj
	@echo ... displaybase.i
	@echo ... displaybase.s
	@echo ... hiddevice.obj
	@echo ... hiddevice.i
	@echo ... hiddevice.s
	@echo ... main.obj
	@echo ... main.i
	@echo ... main.s
	@echo ... mainwindow.obj
	@echo ... mainwindow.i
	@echo ... mainwindow.s
	@echo ... plugin.obj
	@echo ... plugin.i
	@echo ... plugin.s
	@echo ... portbase.obj
	@echo ... portbase.i
	@echo ... portbase.s
	@echo ... porthid.obj
	@echo ... porthid.i
	@echo ... porthid.s
	@echo ... portrs232.obj
	@echo ... portrs232.i
	@echo ... portrs232.s
	@echo ... qrc_resources.obj
	@echo ... qrc_resources.i
	@echo ... qrc_resources.s
	@echo ... serialchartjs.obj
	@echo ... serialchartjs.i
	@echo ... serialchartjs.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

