/****************************************************************************
** Meta object code from reading C++ file 'mainwindow.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.5.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../mainwindow.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'mainwindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.5.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
struct qt_meta_stringdata_MainWindow_t {
    QByteArrayData data[25];
    char stringdata0[491];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_MainWindow_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_MainWindow_t qt_meta_stringdata_MainWindow = {
    {
QT_MOC_LITERAL(0, 0, 10), // "MainWindow"
QT_MOC_LITERAL(1, 11, 21), // "on_sendButton_clicked"
QT_MOC_LITERAL(2, 33, 0), // ""
QT_MOC_LITERAL(3, 34, 30), // "on_actionConfiguration_toggled"
QT_MOC_LITERAL(4, 65, 22), // "on_actionChart_toggled"
QT_MOC_LITERAL(5, 88, 24), // "on_actionToolbar_toggled"
QT_MOC_LITERAL(6, 113, 24), // "on_actionWebView_toggled"
QT_MOC_LITERAL(7, 138, 4), // "arg1"
QT_MOC_LITERAL(8, 143, 23), // "on_actionStop_triggered"
QT_MOC_LITERAL(9, 167, 22), // "on_actionRun_triggered"
QT_MOC_LITERAL(10, 190, 24), // "on_actionAbout_triggered"
QT_MOC_LITERAL(11, 215, 25), // "on_actionSaveAs_triggered"
QT_MOC_LITERAL(12, 241, 23), // "on_actionSave_triggered"
QT_MOC_LITERAL(13, 265, 23), // "on_actionOpen_triggered"
QT_MOC_LITERAL(14, 289, 23), // "on_actionExit_triggered"
QT_MOC_LITERAL(15, 313, 22), // "on_actionNew_triggered"
QT_MOC_LITERAL(16, 336, 32), // "on_configurationText_textChanged"
QT_MOC_LITERAL(17, 369, 11), // "portStopped"
QT_MOC_LITERAL(18, 381, 7), // "message"
QT_MOC_LITERAL(19, 389, 4), // "text"
QT_MOC_LITERAL(20, 394, 4), // "type"
QT_MOC_LITERAL(21, 399, 25), // "on_sendText_returnPressed"
QT_MOC_LITERAL(22, 425, 39), // "mainFrame_javaScriptWindowObj..."
QT_MOC_LITERAL(23, 465, 22), // "mainFrame_loadFinished"
QT_MOC_LITERAL(24, 488, 2) // "ok"

    },
    "MainWindow\0on_sendButton_clicked\0\0"
    "on_actionConfiguration_toggled\0"
    "on_actionChart_toggled\0on_actionToolbar_toggled\0"
    "on_actionWebView_toggled\0arg1\0"
    "on_actionStop_triggered\0on_actionRun_triggered\0"
    "on_actionAbout_triggered\0"
    "on_actionSaveAs_triggered\0"
    "on_actionSave_triggered\0on_actionOpen_triggered\0"
    "on_actionExit_triggered\0on_actionNew_triggered\0"
    "on_configurationText_textChanged\0"
    "portStopped\0message\0text\0type\0"
    "on_sendText_returnPressed\0"
    "mainFrame_javaScriptWindowObjectCleared\0"
    "mainFrame_loadFinished\0ok"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_MainWindow[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
      19,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,  109,    2, 0x08 /* Private */,
       3,    1,  110,    2, 0x08 /* Private */,
       4,    1,  113,    2, 0x08 /* Private */,
       5,    1,  116,    2, 0x08 /* Private */,
       6,    1,  119,    2, 0x08 /* Private */,
       8,    0,  122,    2, 0x08 /* Private */,
       9,    0,  123,    2, 0x08 /* Private */,
      10,    0,  124,    2, 0x08 /* Private */,
      11,    0,  125,    2, 0x08 /* Private */,
      12,    0,  126,    2, 0x08 /* Private */,
      13,    0,  127,    2, 0x08 /* Private */,
      14,    0,  128,    2, 0x08 /* Private */,
      15,    0,  129,    2, 0x08 /* Private */,
      16,    0,  130,    2, 0x08 /* Private */,
      17,    0,  131,    2, 0x08 /* Private */,
      18,    2,  132,    2, 0x08 /* Private */,
      21,    0,  137,    2, 0x08 /* Private */,
      22,    0,  138,    2, 0x08 /* Private */,
      23,    1,  139,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,    2,
    QMetaType::Void, QMetaType::Bool,    2,
    QMetaType::Void, QMetaType::Bool,    2,
    QMetaType::Void, QMetaType::Bool,    7,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   19,   20,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,   24,

       0        // eod
};

void MainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        MainWindow *_t = static_cast<MainWindow *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->on_sendButton_clicked(); break;
        case 1: _t->on_actionConfiguration_toggled((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 2: _t->on_actionChart_toggled((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 3: _t->on_actionToolbar_toggled((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 4: _t->on_actionWebView_toggled((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 5: _t->on_actionStop_triggered(); break;
        case 6: _t->on_actionRun_triggered(); break;
        case 7: _t->on_actionAbout_triggered(); break;
        case 8: _t->on_actionSaveAs_triggered(); break;
        case 9: _t->on_actionSave_triggered(); break;
        case 10: _t->on_actionOpen_triggered(); break;
        case 11: _t->on_actionExit_triggered(); break;
        case 12: _t->on_actionNew_triggered(); break;
        case 13: _t->on_configurationText_textChanged(); break;
        case 14: _t->portStopped(); break;
        case 15: _t->message((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 16: _t->on_sendText_returnPressed(); break;
        case 17: _t->mainFrame_javaScriptWindowObjectCleared(); break;
        case 18: _t->mainFrame_loadFinished((*reinterpret_cast< bool(*)>(_a[1]))); break;
        default: ;
        }
    }
}

const QMetaObject MainWindow::staticMetaObject = {
    { &QMainWindow::staticMetaObject, qt_meta_stringdata_MainWindow.data,
      qt_meta_data_MainWindow,  qt_static_metacall, Q_NULLPTR, Q_NULLPTR}
};


const QMetaObject *MainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return Q_NULLPTR;
    if (!strcmp(_clname, qt_meta_stringdata_MainWindow.stringdata0))
        return static_cast<void*>(const_cast< MainWindow*>(this));
    return QMainWindow::qt_metacast(_clname);
}

int MainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 19)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 19;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 19)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 19;
    }
    return _id;
}
QT_END_MOC_NAMESPACE
