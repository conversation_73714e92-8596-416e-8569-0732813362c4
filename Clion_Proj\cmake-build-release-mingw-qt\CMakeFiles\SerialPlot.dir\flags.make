# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.17

# compile CXX with D:/Libraries/Qt/_Legacy/5.5.1/Tools/mingw492_32/bin/g++.exe
CXX_FLAGS = -O3 -DNDEBUG   -std=gnu++14

CXX_DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_NO_DEBUG -DQT_SERIALPORT_LIB -DQT_WEBKITWIDGETS_LIB -DQT_WEBKIT_LIB -DQT_WIDGETS_LIB

CXX_INCLUDES = @CMakeFiles/SerialPlot.dir/includes_CXX.rsp

