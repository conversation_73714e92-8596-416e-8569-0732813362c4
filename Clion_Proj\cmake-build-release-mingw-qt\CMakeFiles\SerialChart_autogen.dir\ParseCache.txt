# Generated by CMake. Changes will be overwritten.
C:/Users/<USER>/Desktop/SerialChart/decoderhdlc.cpp
C:/Users/<USER>/Desktop/SerialChart/decoderbin.cpp
C:/Users/<USER>/Desktop/SerialChart/chart.h
 mmc:Q_OBJECT
C:/Users/<USER>/Desktop/SerialChart/plugin.cpp
C:/Users/<USER>/Desktop/SerialChart/displaybase.cpp
C:/Users/<USER>/Desktop/SerialChart/decoderplugin.cpp
C:/Users/<USER>/Desktop/SerialChart/configuration.h
 mmc:Q_OBJECT
C:/Users/<USER>/Desktop/SerialChart/decoderbin.h
 mmc:Q_OBJECT
C:/Users/<USER>/Desktop/SerialChart/decoderhdlc.h
 mmc:Q_OBJECT
C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/qrc_resources.cpp
C:/Users/<USER>/Desktop/SerialChart/decoderbase.h
 mmc:Q_OBJECT
C:/Users/<USER>/Desktop/SerialChart/decodercsv.h
 mmc:Q_OBJECT
C:/Users/<USER>/Desktop/SerialChart/plugin.h
 mmc:Q_OBJECT
C:/Users/<USER>/Desktop/SerialChart/decoderplugin.h
 mmc:Q_OBJECT
C:/Users/<USER>/Desktop/SerialChart/displaybase.h
 mmc:Q_OBJECT
C:/Users/<USER>/Desktop/SerialChart/hiddevice.h
C:/Users/<USER>/Desktop/SerialChart/mainwindow.h
 mmc:Q_OBJECT
C:/Users/<USER>/Desktop/SerialChart/portbase.h
 mmc:Q_OBJECT
C:/Users/<USER>/Desktop/SerialChart/porthid.h
 mmc:Q_OBJECT
C:/Users/<USER>/Desktop/SerialChart/portrs232.h
 mmc:Q_OBJECT
C:/Users/<USER>/Desktop/SerialChart/serialchartjs.h
 mmc:Q_OBJECT
C:/Users/<USER>/Desktop/SerialChart/chart.cpp
C:/Users/<USER>/Desktop/SerialChart/configuration.cpp
C:/Users/<USER>/Desktop/SerialChart/decoderbase.cpp
C:/Users/<USER>/Desktop/SerialChart/decodercsv.cpp
C:/Users/<USER>/Desktop/SerialChart/hiddevice.cpp
C:/Users/<USER>/Desktop/SerialChart/main.cpp
 uic:ui_mainwindow.h
C:/Users/<USER>/Desktop/SerialChart/mainwindow.cpp
 uic:ui_mainwindow.h
C:/Users/<USER>/Desktop/SerialChart/portbase.cpp
C:/Users/<USER>/Desktop/SerialChart/porthid.cpp
C:/Users/<USER>/Desktop/SerialChart/portrs232.cpp
C:/Users/<USER>/Desktop/SerialChart/serialchartjs.cpp
