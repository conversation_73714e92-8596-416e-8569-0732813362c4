# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.17

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe"

# The command to remove a file.
RM = "D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Desktop\SerialChart

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt

# Include any dependencies generated for this target.
include CMakeFiles/SerialPlot.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/SerialPlot.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/SerialPlot.dir/flags.make

qrc_resources.cpp: ../images/new.png
qrc_resources.cpp: ../images/open.png
qrc_resources.cpp: ../images/save.png
qrc_resources.cpp: ../images/run.png
qrc_resources.cpp: ../images/stop.png
qrc_resources.cpp: ../resources.qrc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating qrc_resources.cpp"
	D:\Libraries\Qt\_Legacy\5.5.1\5.5\mingw492_32\bin\rcc.exe --name resources --output C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/qrc_resources.cpp C:/Users/<USER>/Desktop/SerialChart/resources.qrc

CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj: SerialPlot_autogen/mocs_compilation.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\SerialPlot_autogen\mocs_compilation.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\SerialPlot_autogen\mocs_compilation.cpp

CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\SerialPlot_autogen\mocs_compilation.cpp > CMakeFiles\SerialPlot.dir\SerialPlot_autogen\mocs_compilation.cpp.i

CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\SerialPlot_autogen\mocs_compilation.cpp -o CMakeFiles\SerialPlot.dir\SerialPlot_autogen\mocs_compilation.cpp.s

CMakeFiles/SerialPlot.dir/chart.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/chart.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/chart.cpp.obj: ../chart.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/SerialPlot.dir/chart.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\chart.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\chart.cpp

CMakeFiles/SerialPlot.dir/chart.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/chart.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\chart.cpp > CMakeFiles\SerialPlot.dir\chart.cpp.i

CMakeFiles/SerialPlot.dir/chart.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/chart.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\chart.cpp -o CMakeFiles\SerialPlot.dir\chart.cpp.s

CMakeFiles/SerialPlot.dir/configuration.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/configuration.cpp.obj: ../configuration.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/SerialPlot.dir/configuration.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\configuration.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\configuration.cpp

CMakeFiles/SerialPlot.dir/configuration.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/configuration.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\configuration.cpp > CMakeFiles\SerialPlot.dir\configuration.cpp.i

CMakeFiles/SerialPlot.dir/configuration.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/configuration.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\configuration.cpp -o CMakeFiles\SerialPlot.dir\configuration.cpp.s

CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj: ../decoderbase.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\decoderbase.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\decoderbase.cpp

CMakeFiles/SerialPlot.dir/decoderbase.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/decoderbase.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\decoderbase.cpp > CMakeFiles\SerialPlot.dir\decoderbase.cpp.i

CMakeFiles/SerialPlot.dir/decoderbase.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/decoderbase.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\decoderbase.cpp -o CMakeFiles\SerialPlot.dir\decoderbase.cpp.s

CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj: ../decoderbin.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\decoderbin.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\decoderbin.cpp

CMakeFiles/SerialPlot.dir/decoderbin.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/decoderbin.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\decoderbin.cpp > CMakeFiles\SerialPlot.dir\decoderbin.cpp.i

CMakeFiles/SerialPlot.dir/decoderbin.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/decoderbin.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\decoderbin.cpp -o CMakeFiles\SerialPlot.dir\decoderbin.cpp.s

CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj: ../decodercsv.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\decodercsv.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\decodercsv.cpp

CMakeFiles/SerialPlot.dir/decodercsv.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/decodercsv.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\decodercsv.cpp > CMakeFiles\SerialPlot.dir\decodercsv.cpp.i

CMakeFiles/SerialPlot.dir/decodercsv.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/decodercsv.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\decodercsv.cpp -o CMakeFiles\SerialPlot.dir\decodercsv.cpp.s

CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj: ../decoderhdlc.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\decoderhdlc.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\decoderhdlc.cpp

CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\decoderhdlc.cpp > CMakeFiles\SerialPlot.dir\decoderhdlc.cpp.i

CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\decoderhdlc.cpp -o CMakeFiles\SerialPlot.dir\decoderhdlc.cpp.s

CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj: ../decoderplugin.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\decoderplugin.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\decoderplugin.cpp

CMakeFiles/SerialPlot.dir/decoderplugin.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/decoderplugin.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\decoderplugin.cpp > CMakeFiles\SerialPlot.dir\decoderplugin.cpp.i

CMakeFiles/SerialPlot.dir/decoderplugin.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/decoderplugin.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\decoderplugin.cpp -o CMakeFiles\SerialPlot.dir\decoderplugin.cpp.s

CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj: ../displaybase.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/SerialPlot.dir/displaybase.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\displaybase.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\displaybase.cpp

CMakeFiles/SerialPlot.dir/displaybase.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/displaybase.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\displaybase.cpp > CMakeFiles\SerialPlot.dir\displaybase.cpp.i

CMakeFiles/SerialPlot.dir/displaybase.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/displaybase.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\displaybase.cpp -o CMakeFiles\SerialPlot.dir\displaybase.cpp.s

CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj: ../hiddevice.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\hiddevice.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\hiddevice.cpp

CMakeFiles/SerialPlot.dir/hiddevice.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/hiddevice.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\hiddevice.cpp > CMakeFiles\SerialPlot.dir\hiddevice.cpp.i

CMakeFiles/SerialPlot.dir/hiddevice.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/hiddevice.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\hiddevice.cpp -o CMakeFiles\SerialPlot.dir\hiddevice.cpp.s

CMakeFiles/SerialPlot.dir/main.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/main.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/main.cpp.obj: ../main.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/SerialPlot.dir/main.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\main.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\main.cpp

CMakeFiles/SerialPlot.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/main.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\main.cpp > CMakeFiles\SerialPlot.dir\main.cpp.i

CMakeFiles/SerialPlot.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/main.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\main.cpp -o CMakeFiles\SerialPlot.dir\main.cpp.s

CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj: ../mainwindow.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\mainwindow.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\mainwindow.cpp

CMakeFiles/SerialPlot.dir/mainwindow.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/mainwindow.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\mainwindow.cpp > CMakeFiles\SerialPlot.dir\mainwindow.cpp.i

CMakeFiles/SerialPlot.dir/mainwindow.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/mainwindow.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\mainwindow.cpp -o CMakeFiles\SerialPlot.dir\mainwindow.cpp.s

CMakeFiles/SerialPlot.dir/plugin.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/plugin.cpp.obj: ../plugin.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object CMakeFiles/SerialPlot.dir/plugin.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\plugin.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\plugin.cpp

CMakeFiles/SerialPlot.dir/plugin.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/plugin.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\plugin.cpp > CMakeFiles\SerialPlot.dir\plugin.cpp.i

CMakeFiles/SerialPlot.dir/plugin.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/plugin.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\plugin.cpp -o CMakeFiles\SerialPlot.dir\plugin.cpp.s

CMakeFiles/SerialPlot.dir/portbase.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/portbase.cpp.obj: ../portbase.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object CMakeFiles/SerialPlot.dir/portbase.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\portbase.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\portbase.cpp

CMakeFiles/SerialPlot.dir/portbase.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/portbase.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\portbase.cpp > CMakeFiles\SerialPlot.dir\portbase.cpp.i

CMakeFiles/SerialPlot.dir/portbase.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/portbase.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\portbase.cpp -o CMakeFiles\SerialPlot.dir\portbase.cpp.s

CMakeFiles/SerialPlot.dir/porthid.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/porthid.cpp.obj: ../porthid.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object CMakeFiles/SerialPlot.dir/porthid.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\porthid.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\porthid.cpp

CMakeFiles/SerialPlot.dir/porthid.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/porthid.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\porthid.cpp > CMakeFiles\SerialPlot.dir\porthid.cpp.i

CMakeFiles/SerialPlot.dir/porthid.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/porthid.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\porthid.cpp -o CMakeFiles\SerialPlot.dir\porthid.cpp.s

CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj: ../portrs232.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object CMakeFiles/SerialPlot.dir/portrs232.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\portrs232.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\portrs232.cpp

CMakeFiles/SerialPlot.dir/portrs232.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/portrs232.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\portrs232.cpp > CMakeFiles\SerialPlot.dir\portrs232.cpp.i

CMakeFiles/SerialPlot.dir/portrs232.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/portrs232.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\portrs232.cpp -o CMakeFiles\SerialPlot.dir\portrs232.cpp.s

CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj: ../serialchartjs.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\serialchartjs.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\serialchartjs.cpp

CMakeFiles/SerialPlot.dir/serialchartjs.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/serialchartjs.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\serialchartjs.cpp > CMakeFiles\SerialPlot.dir\serialchartjs.cpp.i

CMakeFiles/SerialPlot.dir/serialchartjs.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/serialchartjs.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\serialchartjs.cpp -o CMakeFiles\SerialPlot.dir\serialchartjs.cpp.s

CMakeFiles/SerialPlot.dir/qrc_resources.cpp.obj: CMakeFiles/SerialPlot.dir/flags.make
CMakeFiles/SerialPlot.dir/qrc_resources.cpp.obj: CMakeFiles/SerialPlot.dir/includes_CXX.rsp
CMakeFiles/SerialPlot.dir/qrc_resources.cpp.obj: qrc_resources.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object CMakeFiles/SerialPlot.dir/qrc_resources.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialPlot.dir\qrc_resources.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\qrc_resources.cpp

CMakeFiles/SerialPlot.dir/qrc_resources.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialPlot.dir/qrc_resources.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\qrc_resources.cpp > CMakeFiles\SerialPlot.dir\qrc_resources.cpp.i

CMakeFiles/SerialPlot.dir/qrc_resources.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialPlot.dir/qrc_resources.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\qrc_resources.cpp -o CMakeFiles\SerialPlot.dir\qrc_resources.cpp.s

# Object files for target SerialPlot
SerialPlot_OBJECTS = \
"CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj" \
"CMakeFiles/SerialPlot.dir/chart.cpp.obj" \
"CMakeFiles/SerialPlot.dir/configuration.cpp.obj" \
"CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj" \
"CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj" \
"CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj" \
"CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj" \
"CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj" \
"CMakeFiles/SerialPlot.dir/displaybase.cpp.obj" \
"CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj" \
"CMakeFiles/SerialPlot.dir/main.cpp.obj" \
"CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj" \
"CMakeFiles/SerialPlot.dir/plugin.cpp.obj" \
"CMakeFiles/SerialPlot.dir/portbase.cpp.obj" \
"CMakeFiles/SerialPlot.dir/porthid.cpp.obj" \
"CMakeFiles/SerialPlot.dir/portrs232.cpp.obj" \
"CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj" \
"CMakeFiles/SerialPlot.dir/qrc_resources.cpp.obj"

# External object files for target SerialPlot
SerialPlot_EXTERNAL_OBJECTS =

SerialPlot.exe: CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/chart.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/configuration.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/displaybase.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/main.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/plugin.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/portbase.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/porthid.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/portrs232.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/qrc_resources.cpp.obj
SerialPlot.exe: CMakeFiles/SerialPlot.dir/build.make
SerialPlot.exe: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/libQt5SerialPort.a
SerialPlot.exe: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/libQt5WebKitWidgets.a
SerialPlot.exe: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/libQt5Widgets.a
SerialPlot.exe: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/libQt5WebKit.a
SerialPlot.exe: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/libQt5Gui.a
SerialPlot.exe: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/libQt5Network.a
SerialPlot.exe: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/libQt5Core.a
SerialPlot.exe: CMakeFiles/SerialPlot.dir/linklibs.rsp
SerialPlot.exe: CMakeFiles/SerialPlot.dir/objects1.rsp
SerialPlot.exe: CMakeFiles/SerialPlot.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Linking CXX executable SerialPlot.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\SerialPlot.dir\link.txt --verbose=$(VERBOSE)
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E make_directory C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/plugins/platforms/
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E copy D:\Libraries\Qt\_Legacy\5.5.1\5.5\mingw492_32\lib\cmake/../../plugins/platforms/qwindows.dll C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/plugins/platforms/
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E copy D:\Libraries\Qt\_Legacy\5.5.1\5.5\mingw492_32\lib\cmake/../../bin/Qt5Core.dll C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E copy D:\Libraries\Qt\_Legacy\5.5.1\5.5\mingw492_32\lib\cmake/../../bin/Qt5Gui.dll C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E copy D:\Libraries\Qt\_Legacy\5.5.1\5.5\mingw492_32\lib\cmake/../../bin/Qt5Widgets.dll C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E copy D:\Libraries\Qt\_Legacy\5.5.1\5.5\mingw492_32\lib\cmake/../../bin/Qt5SerialPort.dll C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E copy D:\Libraries\Qt\_Legacy\5.5.1\5.5\mingw492_32\lib\cmake/../../bin/Qt5WebKit.dll C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E copy D:\Libraries\Qt\_Legacy\5.5.1\5.5\mingw492_32\lib\cmake/../../bin/Qt5WebKitWidgets.dll C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt

# Rule to build all files generated by this target.
CMakeFiles/SerialPlot.dir/build: SerialPlot.exe

.PHONY : CMakeFiles/SerialPlot.dir/build

CMakeFiles/SerialPlot.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\SerialPlot.dir\cmake_clean.cmake
.PHONY : CMakeFiles/SerialPlot.dir/clean

CMakeFiles/SerialPlot.dir/depend: qrc_resources.cpp
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\Users\<USER>\Desktop\SerialChart C:\Users\<USER>\Desktop\SerialChart C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles\SerialPlot.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/SerialPlot.dir/depend

