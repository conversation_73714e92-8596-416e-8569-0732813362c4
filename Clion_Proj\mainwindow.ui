<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>976</width>
    <height>633</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Serial Chart</string>
  </property>
  <property name="windowIcon">
   <iconset resource="resources.qrc">
    <normaloff>:/images/run.png</normaloff>:/images/run.png</iconset>
  </property>
  <widget class="QWidget" name="centralWidget">
   <layout class="QHBoxLayout" name="horizontalLayout">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QFrame" name="frame">
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QFrame" name="frame_2">
         <property name="autoFillBackground">
          <bool>false</bool>
         </property>
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_4">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>3</number>
          </property>
          <property name="topMargin">
           <number>3</number>
          </property>
          <property name="rightMargin">
           <number>3</number>
          </property>
          <property name="bottomMargin">
           <number>3</number>
          </property>
          <item>
           <widget class="QLabel" name="label">
            <property name="autoFillBackground">
             <bool>true</bool>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="text">
             <string>Data</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QPlainTextEdit" name="dataText">
         <property name="font">
          <font>
           <family>Courier</family>
           <pointsize>10</pointsize>
          </font>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="frame_3">
         <property name="autoFillBackground">
          <bool>true</bool>
         </property>
         <property name="frameShape">
          <enum>QFrame::Panel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QLineEdit" name="sendText">
            <property name="enabled">
             <bool>true</bool>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="alignment">
             <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Fixed</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>10</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QCheckBox" name="checkBoxCR">
            <property name="text">
             <string>\ r</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QCheckBox" name="checkBoxLF">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string>\ n</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QCheckBox" name="checkBoxEcho">
            <property name="text">
             <string>echo</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="sendButton">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="text">
             <string>Send</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menuBar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>976</width>
     <height>26</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>&amp;文件</string>
    </property>
    <addaction name="actionNew"/>
    <addaction name="actionOpen"/>
    <addaction name="actionSave"/>
    <addaction name="actionSaveAs"/>
    <addaction name="separator"/>
    <addaction name="actionExit"/>
   </widget>
   <widget class="QMenu" name="menuProcess">
    <property name="title">
     <string>操作</string>
    </property>
    <addaction name="actionRun"/>
    <addaction name="actionStop"/>
   </widget>
   <widget class="QMenu" name="menu_Help">
    <property name="title">
     <string>&amp;帮助</string>
    </property>
    <addaction name="actionAbout"/>
   </widget>
   <widget class="QMenu" name="menuView">
    <property name="title">
     <string>视图</string>
    </property>
    <addaction name="actionToolbar"/>
    <addaction name="separator"/>
    <addaction name="actionConfiguration"/>
    <addaction name="actionChart"/>
    <addaction name="actionWebView"/>
    <addaction name="separator"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuProcess"/>
   <addaction name="menuView"/>
   <addaction name="menu_Help"/>
  </widget>
  <widget class="QToolBar" name="mainToolBar">
   <property name="windowTitle">
    <string>Toolbar</string>
   </property>
   <property name="movable">
    <bool>false</bool>
   </property>
   <property name="allowedAreas">
    <set>Qt::TopToolBarArea</set>
   </property>
   <property name="floatable">
    <bool>false</bool>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionNew"/>
   <addaction name="actionOpen"/>
   <addaction name="actionSave"/>
   <addaction name="separator"/>
   <addaction name="actionRun"/>
   <addaction name="actionStop"/>
  </widget>
  <widget class="QStatusBar" name="statusBar"/>
  <widget class="QDockWidget" name="dockWidgetChart">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="minimumSize">
    <size>
     <width>100</width>
     <height>201</height>
    </size>
   </property>
   <property name="features">
    <set>QDockWidget::DockWidgetFloatable|QDockWidget::DockWidgetMovable</set>
   </property>
   <property name="windowTitle">
    <string>Chart</string>
   </property>
   <attribute name="dockWidgetArea">
    <number>8</number>
   </attribute>
   <widget class="QWidget" name="dockWidgetContentsChart">
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <property name="spacing">
      <number>0</number>
     </property>
     <property name="leftMargin">
      <number>0</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QScrollArea" name="scrollArea">
       <property name="autoFillBackground">
        <bool>false</bool>
       </property>
       <property name="frameShape">
        <enum>QFrame::NoFrame</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Plain</enum>
       </property>
       <property name="lineWidth">
        <number>0</number>
       </property>
       <property name="verticalScrollBarPolicy">
        <enum>Qt::ScrollBarAsNeeded</enum>
       </property>
       <property name="horizontalScrollBarPolicy">
        <enum>Qt::ScrollBarAsNeeded</enum>
       </property>
       <property name="widgetResizable">
        <bool>true</bool>
       </property>
       <widget class="QWidget" name="scrollAreaWidgetContents">
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>108</width>
          <height>374</height>
         </rect>
        </property>
        <layout class="QFormLayout" name="formLayout">
         <property name="horizontalSpacing">
          <number>0</number>
         </property>
         <property name="verticalSpacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item row="0" column="0">
          <widget class="Chart" name="chart" native="true">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>100</width>
             <height>100</height>
            </size>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </widget>
     </item>
    </layout>
   </widget>
  </widget>
  <widget class="QDockWidget" name="dockWidgetConfiguration">
   <property name="features">
    <set>QDockWidget::DockWidgetFloatable|QDockWidget::DockWidgetMovable</set>
   </property>
   <property name="windowTitle">
    <string>Configuration</string>
   </property>
   <attribute name="dockWidgetArea">
    <number>2</number>
   </attribute>
   <widget class="QWidget" name="dockWidgetContentsConfiguration">
    <layout class="QVBoxLayout" name="verticalLayout_3">
     <property name="spacing">
      <number>0</number>
     </property>
     <property name="leftMargin">
      <number>0</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QPlainTextEdit" name="configurationText">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="font">
        <font>
         <family>Courier</family>
         <pointsize>10</pointsize>
        </font>
       </property>
       <property name="documentTitle">
        <string/>
       </property>
       <property name="readOnly">
        <bool>false</bool>
       </property>
       <property name="plainText">
        <string/>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
  </widget>
  <widget class="QDockWidget" name="dockWidgetWebView">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="minimumSize">
    <size>
     <width>100</width>
     <height>100</height>
    </size>
   </property>
   <property name="features">
    <set>QDockWidget::DockWidgetFloatable|QDockWidget::DockWidgetMovable</set>
   </property>
   <property name="windowTitle">
    <string>WebView</string>
   </property>
   <attribute name="dockWidgetArea">
    <number>8</number>
   </attribute>
   <widget class="QWidget" name="dockWidgetContentsWebView">
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <property name="spacing">
      <number>0</number>
     </property>
     <property name="leftMargin">
      <number>0</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QWebView" name="webView">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="url">
        <url>
         <string>about:blank</string>
        </url>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
  </widget>
  <action name="actionOpen">
   <property name="icon">
    <iconset>
     <normalon>:/images/open.png</normalon>
    </iconset>
   </property>
   <property name="text">
    <string>打开配置</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+O</string>
   </property>
  </action>
  <action name="actionExit">
   <property name="text">
    <string>E&amp;xit</string>
   </property>
  </action>
  <action name="actionSave">
   <property name="icon">
    <iconset>
     <normalon>:/images/save.png</normalon>
    </iconset>
   </property>
   <property name="text">
    <string>保存配置</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+S</string>
   </property>
  </action>
  <action name="actionNew">
   <property name="icon">
    <iconset resource="resources.qrc">
     <normaloff>:/images/new.png</normaloff>:/images/new.png</iconset>
   </property>
   <property name="text">
    <string>新建配置</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+N</string>
   </property>
  </action>
  <action name="actionSaveAs">
   <property name="text">
    <string>另存为配置</string>
   </property>
  </action>
  <action name="actionRun">
   <property name="icon">
    <iconset resource="resources.qrc">
     <normaloff>:/images/run.png</normaloff>:/images/run.png</iconset>
   </property>
   <property name="text">
    <string>Run</string>
   </property>
   <property name="shortcut">
    <string>F5</string>
   </property>
  </action>
  <action name="actionStop">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="resources.qrc">
     <normaloff>:/images/stop.png</normaloff>:/images/stop.png</iconset>
   </property>
   <property name="text">
    <string>&amp;Stop</string>
   </property>
   <property name="shortcut">
    <string>F6</string>
   </property>
  </action>
  <action name="actionAbout">
   <property name="text">
    <string>&amp;关于</string>
   </property>
  </action>
  <action name="actionConfiguration">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Configuration</string>
   </property>
  </action>
  <action name="actionChart">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Chart</string>
   </property>
  </action>
  <action name="actionToolbar">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Toolbar</string>
   </property>
  </action>
  <action name="actionWebView">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>WebView</string>
   </property>
  </action>
  <zorder>dockWidgetWebView</zorder>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>QWebView</class>
   <extends>QWidget</extends>
   <header>QtWebKitWidgets/QWebView</header>
  </customwidget>
  <customwidget>
   <class>Chart</class>
   <extends>QWidget</extends>
   <header>chart.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="resources.qrc"/>
 </resources>
 <connections/>
</ui>
