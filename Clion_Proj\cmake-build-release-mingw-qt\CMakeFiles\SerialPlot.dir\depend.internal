# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.17

CMakeFiles/SerialPlot.dir/SerialPlot_autogen/mocs_compilation.cpp.obj
 C:/Users/<USER>/Desktop/SerialChart/chart.h
 C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/SerialPlot_autogen/EWIEGA46WW/moc_chart.cpp
 C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/SerialPlot_autogen/EWIEGA46WW/moc_configuration.cpp
 C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/SerialPlot_autogen/EWIEGA46WW/moc_decoderbase.cpp
 C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/SerialPlot_autogen/EWIEGA46WW/moc_decoderbin.cpp
 C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/SerialPlot_autogen/EWIEGA46WW/moc_decodercsv.cpp
 C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/SerialPlot_autogen/EWIEGA46WW/moc_decoderhdlc.cpp
 C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/SerialPlot_autogen/EWIEGA46WW/moc_decoderplugin.cpp
 C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/SerialPlot_autogen/EWIEGA46WW/moc_displaybase.cpp
 C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/SerialPlot_autogen/EWIEGA46WW/moc_mainwindow.cpp
 C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/SerialPlot_autogen/EWIEGA46WW/moc_plugin.cpp
 C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/SerialPlot_autogen/EWIEGA46WW/moc_portbase.cpp
 C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/SerialPlot_autogen/EWIEGA46WW/moc_porthid.cpp
 C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/SerialPlot_autogen/EWIEGA46WW/moc_portrs232.cpp
 C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/SerialPlot_autogen/EWIEGA46WW/moc_serialchartjs.cpp
 C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/SerialPlot_autogen/mocs_compilation.cpp
 C:/Users/<USER>/Desktop/SerialChart/common.h
 C:/Users/<USER>/Desktop/SerialChart/configuration.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbase.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbin.h
 C:/Users/<USER>/Desktop/SerialChart/decodercsv.h
 C:/Users/<USER>/Desktop/SerialChart/decoderhdlc.h
 C:/Users/<USER>/Desktop/SerialChart/decoderplugin.h
 C:/Users/<USER>/Desktop/SerialChart/displaybase.h
 C:/Users/<USER>/Desktop/SerialChart/hiddevice.h
 C:/Users/<USER>/Desktop/SerialChart/mainwindow.h
 C:/Users/<USER>/Desktop/SerialChart/plugin.h
 C:/Users/<USER>/Desktop/SerialChart/portbase.h
 C:/Users/<USER>/Desktop/SerialChart/porthid.h
 C:/Users/<USER>/Desktop/SerialChart/portrs232.h
 C:/Users/<USER>/Desktop/SerialChart/serialchartjs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QDebug
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QFlags
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMetaType
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSharedDataPointer
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcryptographichash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatetime.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslConfiguration
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslPreSharedKeyAuthenticator
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qabstractsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qnetworkaccessmanager.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qssl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslcertificate.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslconfiguration.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslerror.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslpresharedkeyauthenticator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qtcpsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebkitglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/QWebView
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebpage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebview.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QWidget
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
CMakeFiles/SerialPlot.dir/chart.cpp.obj
 C:/Users/<USER>/Desktop/SerialChart/chart.cpp
 C:/Users/<USER>/Desktop/SerialChart/chart.h
 C:/Users/<USER>/Desktop/SerialChart/common.h
 C:/Users/<USER>/Desktop/SerialChart/configuration.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbase.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QWidget
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
CMakeFiles/SerialPlot.dir/configuration.cpp.obj
 C:/Users/<USER>/Desktop/SerialChart/common.h
 C:/Users/<USER>/Desktop/SerialChart/configuration.cpp
 C:/Users/<USER>/Desktop/SerialChart/configuration.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
CMakeFiles/SerialPlot.dir/decoderbase.cpp.obj
 C:/Users/<USER>/Desktop/SerialChart/common.h
 C:/Users/<USER>/Desktop/SerialChart/configuration.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbase.cpp
 C:/Users/<USER>/Desktop/SerialChart/decoderbase.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbin.h
 C:/Users/<USER>/Desktop/SerialChart/decodercsv.h
 C:/Users/<USER>/Desktop/SerialChart/decoderhdlc.h
 C:/Users/<USER>/Desktop/SerialChart/decoderplugin.h
 C:/Users/<USER>/Desktop/SerialChart/displaybase.h
 C:/Users/<USER>/Desktop/SerialChart/mainwindow.h
 C:/Users/<USER>/Desktop/SerialChart/portbase.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
CMakeFiles/SerialPlot.dir/decoderbin.cpp.obj
 C:/Users/<USER>/Desktop/SerialChart/common.h
 C:/Users/<USER>/Desktop/SerialChart/configuration.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbase.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbin.cpp
 C:/Users/<USER>/Desktop/SerialChart/decoderbin.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
CMakeFiles/SerialPlot.dir/decodercsv.cpp.obj
 C:/Users/<USER>/Desktop/SerialChart/common.h
 C:/Users/<USER>/Desktop/SerialChart/configuration.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbase.h
 C:/Users/<USER>/Desktop/SerialChart/decodercsv.cpp
 C:/Users/<USER>/Desktop/SerialChart/decodercsv.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
CMakeFiles/SerialPlot.dir/decoderhdlc.cpp.obj
 C:/Users/<USER>/Desktop/SerialChart/common.h
 C:/Users/<USER>/Desktop/SerialChart/configuration.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbase.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbin.h
 C:/Users/<USER>/Desktop/SerialChart/decoderhdlc.cpp
 C:/Users/<USER>/Desktop/SerialChart/decoderhdlc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
CMakeFiles/SerialPlot.dir/decoderplugin.cpp.obj
 C:/Users/<USER>/Desktop/SerialChart/common.h
 C:/Users/<USER>/Desktop/SerialChart/configuration.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbase.h
 C:/Users/<USER>/Desktop/SerialChart/decoderplugin.cpp
 C:/Users/<USER>/Desktop/SerialChart/decoderplugin.h
 C:/Users/<USER>/Desktop/SerialChart/displaybase.h
 C:/Users/<USER>/Desktop/SerialChart/hiddevice.h
 C:/Users/<USER>/Desktop/SerialChart/mainwindow.h
 C:/Users/<USER>/Desktop/SerialChart/plugin.h
 C:/Users/<USER>/Desktop/SerialChart/portbase.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QDebug
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QFlags
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMetaType
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSharedDataPointer
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcryptographichash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatetime.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslConfiguration
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslPreSharedKeyAuthenticator
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qabstractsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qnetworkaccessmanager.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qssl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslcertificate.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslconfiguration.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslerror.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslpresharedkeyauthenticator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qtcpsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebkitglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/QWebView
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebpage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebview.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
CMakeFiles/SerialPlot.dir/displaybase.cpp.obj
 C:/Users/<USER>/Desktop/SerialChart/common.h
 C:/Users/<USER>/Desktop/SerialChart/configuration.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbase.h
 C:/Users/<USER>/Desktop/SerialChart/displaybase.cpp
 C:/Users/<USER>/Desktop/SerialChart/displaybase.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
CMakeFiles/SerialPlot.dir/hiddevice.cpp.obj
 C:/Users/<USER>/Desktop/SerialChart/hiddevice.cpp
 C:/Users/<USER>/Desktop/SerialChart/hiddevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
CMakeFiles/SerialPlot.dir/main.cpp.obj
 ../chart.h
 C:/Users/<USER>/Desktop/SerialChart/common.h
 C:/Users/<USER>/Desktop/SerialChart/configuration.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbase.h
 C:/Users/<USER>/Desktop/SerialChart/displaybase.h
 C:/Users/<USER>/Desktop/SerialChart/main.cpp
 C:/Users/<USER>/Desktop/SerialChart/mainwindow.h
 C:/Users/<USER>/Desktop/SerialChart/portbase.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QFlags
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMetaType
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSharedDataPointer
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QVariant
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qabstractitemmodel.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcryptographichash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatetime.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qitemselectionmodel.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregularexpression.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qabstracttextdocumentlayout.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontdatabase.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qglyphrun.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qguiapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qinputmethod.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrawfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextdocument.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextlayout.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvalidator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslConfiguration
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslPreSharedKeyAuthenticator
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qabstractsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qnetworkaccessmanager.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qssl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslcertificate.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslconfiguration.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslerror.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslpresharedkeyauthenticator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qtcpsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebkitglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/QWebView
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebpage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebview.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QAction
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QButtonGroup
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QCheckBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QDockWidget
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFormLayout
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFrame
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QHBoxLayout
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QHeaderView
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QLabel
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QLayout
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QLineEdit
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMenu
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMenuBar
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QPlainTextEdit
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QPushButton
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QScrollArea
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QSpacerItem
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QStatusBar
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QToolBar
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QVBoxLayout
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QWidget
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractbutton.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractitemdelegate.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractitemview.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractscrollarea.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractslider.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractspinbox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qaction.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qactiongroup.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qboxlayout.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qbuttongroup.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qcheckbox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdesktopwidget.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdockwidget.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qformlayout.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qframe.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qgridlayout.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qheaderview.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qlabel.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qlayout.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qlayoutitem.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qlineedit.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmenu.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmenubar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qplaintextedit.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qpushbutton.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qrubberband.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qscrollarea.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qslider.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qstatusbar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qstyle.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qstyleoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabbar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtextedit.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtoolbar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
 SerialPlot_autogen/include/ui_mainwindow.h
CMakeFiles/SerialPlot.dir/mainwindow.cpp.obj
 ../chart.h
 C:/Users/<USER>/Desktop/SerialChart/common.h
 C:/Users/<USER>/Desktop/SerialChart/configuration.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbase.h
 C:/Users/<USER>/Desktop/SerialChart/decoderplugin.h
 C:/Users/<USER>/Desktop/SerialChart/displaybase.h
 C:/Users/<USER>/Desktop/SerialChart/hiddevice.h
 C:/Users/<USER>/Desktop/SerialChart/mainwindow.cpp
 C:/Users/<USER>/Desktop/SerialChart/mainwindow.h
 C:/Users/<USER>/Desktop/SerialChart/plugin.h
 C:/Users/<USER>/Desktop/SerialChart/portbase.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QDebug
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QFlags
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMetaType
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSharedDataPointer
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QVariant
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qabstractitemmodel.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcryptographichash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatetime.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qitemselectionmodel.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregularexpression.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qabstracttextdocumentlayout.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontdatabase.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qglyphrun.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qguiapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qinputmethod.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrawfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextdocument.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextlayout.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvalidator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslConfiguration
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslPreSharedKeyAuthenticator
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qabstractsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qnetworkaccessmanager.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qssl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslcertificate.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslconfiguration.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslerror.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslpresharedkeyauthenticator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qtcpsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebkitglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/QWebFrame
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/QWebView
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebframe.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebpage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebview.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QAction
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QButtonGroup
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QCheckBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QDockWidget
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFormLayout
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFrame
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QHBoxLayout
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QHeaderView
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QLabel
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QLayout
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QLineEdit
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMenu
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMenuBar
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QPlainTextEdit
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QPushButton
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QScrollArea
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QSpacerItem
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QStatusBar
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QToolBar
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QVBoxLayout
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QWidget
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractbutton.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractitemdelegate.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractitemview.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractscrollarea.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractslider.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qabstractspinbox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qaction.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qactiongroup.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qboxlayout.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qbuttongroup.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qcheckbox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdesktopwidget.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdockwidget.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qformlayout.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qframe.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qgridlayout.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qheaderview.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qlabel.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qlayout.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qlayoutitem.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qlineedit.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmenu.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmenubar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qplaintextedit.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qpushbutton.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qrubberband.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qscrollarea.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qslider.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qstatusbar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qstyle.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qstyleoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabbar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtextedit.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtoolbar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
 SerialPlot_autogen/include/ui_mainwindow.h
CMakeFiles/SerialPlot.dir/plugin.cpp.obj
 C:/Users/<USER>/Desktop/SerialChart/common.h
 C:/Users/<USER>/Desktop/SerialChart/configuration.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbase.h
 C:/Users/<USER>/Desktop/SerialChart/decoderplugin.h
 C:/Users/<USER>/Desktop/SerialChart/displaybase.h
 C:/Users/<USER>/Desktop/SerialChart/hiddevice.h
 C:/Users/<USER>/Desktop/SerialChart/mainwindow.h
 C:/Users/<USER>/Desktop/SerialChart/plugin.cpp
 C:/Users/<USER>/Desktop/SerialChart/plugin.h
 C:/Users/<USER>/Desktop/SerialChart/portbase.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QDebug
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QFlags
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QJsonArray
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QJsonDocument
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMetaType
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSharedDataPointer
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcryptographichash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatetime.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qjsonarray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qjsondocument.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qjsonvalue.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslConfiguration
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslPreSharedKeyAuthenticator
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qabstractsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qnetworkaccessmanager.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qssl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslcertificate.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslconfiguration.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslerror.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslpresharedkeyauthenticator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qtcpsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebkitglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/QWebView
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebpage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebview.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QWidget
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
CMakeFiles/SerialPlot.dir/portbase.cpp.obj
 C:/Users/<USER>/Desktop/SerialChart/common.h
 C:/Users/<USER>/Desktop/SerialChart/configuration.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbase.h
 C:/Users/<USER>/Desktop/SerialChart/hiddevice.h
 C:/Users/<USER>/Desktop/SerialChart/portbase.cpp
 C:/Users/<USER>/Desktop/SerialChart/portbase.h
 C:/Users/<USER>/Desktop/SerialChart/porthid.h
 C:/Users/<USER>/Desktop/SerialChart/portrs232.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
CMakeFiles/SerialPlot.dir/porthid.cpp.obj
 C:/Users/<USER>/Desktop/SerialChart/common.h
 C:/Users/<USER>/Desktop/SerialChart/configuration.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbase.h
 C:/Users/<USER>/Desktop/SerialChart/displaybase.h
 C:/Users/<USER>/Desktop/SerialChart/hiddevice.h
 C:/Users/<USER>/Desktop/SerialChart/mainwindow.h
 C:/Users/<USER>/Desktop/SerialChart/portbase.h
 C:/Users/<USER>/Desktop/SerialChart/porthid.cpp
 C:/Users/<USER>/Desktop/SerialChart/porthid.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
CMakeFiles/SerialPlot.dir/portrs232.cpp.obj
 C:/Users/<USER>/Desktop/SerialChart/common.h
 C:/Users/<USER>/Desktop/SerialChart/configuration.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbase.h
 C:/Users/<USER>/Desktop/SerialChart/displaybase.h
 C:/Users/<USER>/Desktop/SerialChart/mainwindow.h
 C:/Users/<USER>/Desktop/SerialChart/portbase.h
 C:/Users/<USER>/Desktop/SerialChart/portrs232.cpp
 C:/Users/<USER>/Desktop/SerialChart/portrs232.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QDebug
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
CMakeFiles/SerialPlot.dir/qrc_resources.cpp.obj
 C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/qrc_resources.cpp
CMakeFiles/SerialPlot.dir/serialchartjs.cpp.obj
 C:/Users/<USER>/Desktop/SerialChart/common.h
 C:/Users/<USER>/Desktop/SerialChart/configuration.h
 C:/Users/<USER>/Desktop/SerialChart/decoderbase.h
 C:/Users/<USER>/Desktop/SerialChart/displaybase.h
 C:/Users/<USER>/Desktop/SerialChart/hiddevice.h
 C:/Users/<USER>/Desktop/SerialChart/mainwindow.h
 C:/Users/<USER>/Desktop/SerialChart/portbase.h
 C:/Users/<USER>/Desktop/SerialChart/serialchartjs.cpp
 C:/Users/<USER>/Desktop/SerialChart/serialchartjs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QByteArray
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QCoreApplication
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QDebug
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QFlags
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QHash
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QIODevice
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QList
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMetaType
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QMutex
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QObject
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QPair
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSettings
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QSharedDataPointer
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QString
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QTextStream
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QThread
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/QtGlobal
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qalgorithms.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qarraydata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv5.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv6.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_armv7.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_bootstrap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_cxx11.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_gcc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_ia64.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_mips.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_msvc.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_unix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qatomic_x86.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbasicatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qbytearraylist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qchar.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcompilerdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qconfig.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontainerfwd.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcontiguouscache.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreapplication.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcoreevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qcryptographichash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatastream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdatetime.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdebug.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qdir.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qeventloop.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfeatures.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfile.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfiledevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qfileinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qflags.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qgenericatomic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qglobalstatic.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qhash.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiodevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qisenum.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qiterator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qline.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlocale.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qlogging.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmargins.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmetatype.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qmutex.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnamespace.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qnumeric.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobject_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qobjectdefs_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpair.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qpoint.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qprocessordetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrect.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qrefcount.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qregexp.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qscopedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qset.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qshareddata.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsharedpointer_impl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsize.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstring.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringbuilder.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringlist.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qstringmatcher.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsysinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qsystemdetection.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtextstream.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qthread.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypeinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qtypetraits.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qurlquery.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvariant.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvarlengtharray.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtCore/qvector.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/QPainter
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qbrush.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcolor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qcursor.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qevent.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfont.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qfontmetrics.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qicon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qimage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qkeysequence.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qmatrix.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpaintdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainter.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpainterpath.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpalette.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpen.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixelformat.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpixmap.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qpolygon.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qregion.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qrgb.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtextoption.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtouchdevice.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qtransform.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qvector2d.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtGui/qwindowdefs_win.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslConfiguration
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/QSslPreSharedKeyAuthenticator
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qabstractsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qnetworkaccessmanager.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qssl.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslcertificate.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslconfiguration.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslerror.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslpresharedkeyauthenticator.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qsslsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtNetwork/qtcpsocket.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPort
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/QSerialPortInfo
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialport.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtSerialPort/qserialportinfo.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebkitglobal.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKit/qwebsettings.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/QWebView
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebpage.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWebKitWidgets/qwebview.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QFileDialog
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMainWindow
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QMessageBox
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/QWidget
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qdialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qfiledialog.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmainwindow.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qmessagebox.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qsizepolicy.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qtabwidget.h
 D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/include/QtWidgets/qwidget.h
