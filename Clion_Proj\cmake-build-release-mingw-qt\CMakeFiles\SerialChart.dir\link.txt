"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E rm -f CMakeFiles\SerialChart.dir/objects.a
D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\ar.exe cr CMakeFiles\SerialChart.dir/objects.a @CMakeFiles\SerialChart.dir\objects1.rsp
D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe -O3 -DNDEBUG   -Wl,--whole-archive CMakeFiles\SerialChart.dir/objects.a -Wl,--no-whole-archive  -o SerialChart.exe -Wl,--out-implib,libSerialChart.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\SerialChart.dir\linklibs.rsp
