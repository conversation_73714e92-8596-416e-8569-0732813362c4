# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.17

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe"

# The command to remove a file.
RM = "D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Desktop\SerialChart

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt

# Include any dependencies generated for this target.
include CMakeFiles/SerialChart.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/SerialChart.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/SerialChart.dir/flags.make

qrc_resources.cpp: ../images/new.png
qrc_resources.cpp: ../images/open.png
qrc_resources.cpp: ../images/save.png
qrc_resources.cpp: ../images/run.png
qrc_resources.cpp: ../images/stop.png
qrc_resources.cpp: ../resources.qrc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating qrc_resources.cpp"
	D:\Libraries\Qt\_Legacy\5.5.1\5.5\mingw492_32\bin\rcc.exe --name resources --output C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/qrc_resources.cpp C:/Users/<USER>/Desktop/SerialChart/resources.qrc

CMakeFiles/SerialChart.dir/SerialChart_autogen/mocs_compilation.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/SerialChart_autogen/mocs_compilation.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/SerialChart_autogen/mocs_compilation.cpp.obj: SerialChart_autogen/mocs_compilation.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/SerialChart.dir/SerialChart_autogen/mocs_compilation.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\SerialChart_autogen\mocs_compilation.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\SerialChart_autogen\mocs_compilation.cpp

CMakeFiles/SerialChart.dir/SerialChart_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/SerialChart_autogen/mocs_compilation.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\SerialChart_autogen\mocs_compilation.cpp > CMakeFiles\SerialChart.dir\SerialChart_autogen\mocs_compilation.cpp.i

CMakeFiles/SerialChart.dir/SerialChart_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/SerialChart_autogen/mocs_compilation.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\SerialChart_autogen\mocs_compilation.cpp -o CMakeFiles\SerialChart.dir\SerialChart_autogen\mocs_compilation.cpp.s

CMakeFiles/SerialChart.dir/chart.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/chart.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/chart.cpp.obj: ../chart.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/SerialChart.dir/chart.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\chart.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\chart.cpp

CMakeFiles/SerialChart.dir/chart.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/chart.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\chart.cpp > CMakeFiles\SerialChart.dir\chart.cpp.i

CMakeFiles/SerialChart.dir/chart.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/chart.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\chart.cpp -o CMakeFiles\SerialChart.dir\chart.cpp.s

CMakeFiles/SerialChart.dir/configuration.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/configuration.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/configuration.cpp.obj: ../configuration.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/SerialChart.dir/configuration.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\configuration.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\configuration.cpp

CMakeFiles/SerialChart.dir/configuration.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/configuration.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\configuration.cpp > CMakeFiles\SerialChart.dir\configuration.cpp.i

CMakeFiles/SerialChart.dir/configuration.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/configuration.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\configuration.cpp -o CMakeFiles\SerialChart.dir\configuration.cpp.s

CMakeFiles/SerialChart.dir/decoderbase.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/decoderbase.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/decoderbase.cpp.obj: ../decoderbase.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/SerialChart.dir/decoderbase.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\decoderbase.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\decoderbase.cpp

CMakeFiles/SerialChart.dir/decoderbase.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/decoderbase.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\decoderbase.cpp > CMakeFiles\SerialChart.dir\decoderbase.cpp.i

CMakeFiles/SerialChart.dir/decoderbase.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/decoderbase.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\decoderbase.cpp -o CMakeFiles\SerialChart.dir\decoderbase.cpp.s

CMakeFiles/SerialChart.dir/decoderbin.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/decoderbin.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/decoderbin.cpp.obj: ../decoderbin.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/SerialChart.dir/decoderbin.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\decoderbin.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\decoderbin.cpp

CMakeFiles/SerialChart.dir/decoderbin.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/decoderbin.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\decoderbin.cpp > CMakeFiles\SerialChart.dir\decoderbin.cpp.i

CMakeFiles/SerialChart.dir/decoderbin.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/decoderbin.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\decoderbin.cpp -o CMakeFiles\SerialChart.dir\decoderbin.cpp.s

CMakeFiles/SerialChart.dir/decodercsv.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/decodercsv.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/decodercsv.cpp.obj: ../decodercsv.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/SerialChart.dir/decodercsv.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\decodercsv.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\decodercsv.cpp

CMakeFiles/SerialChart.dir/decodercsv.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/decodercsv.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\decodercsv.cpp > CMakeFiles\SerialChart.dir\decodercsv.cpp.i

CMakeFiles/SerialChart.dir/decodercsv.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/decodercsv.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\decodercsv.cpp -o CMakeFiles\SerialChart.dir\decodercsv.cpp.s

CMakeFiles/SerialChart.dir/decoderhdlc.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/decoderhdlc.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/decoderhdlc.cpp.obj: ../decoderhdlc.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/SerialChart.dir/decoderhdlc.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\decoderhdlc.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\decoderhdlc.cpp

CMakeFiles/SerialChart.dir/decoderhdlc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/decoderhdlc.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\decoderhdlc.cpp > CMakeFiles\SerialChart.dir\decoderhdlc.cpp.i

CMakeFiles/SerialChart.dir/decoderhdlc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/decoderhdlc.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\decoderhdlc.cpp -o CMakeFiles\SerialChart.dir\decoderhdlc.cpp.s

CMakeFiles/SerialChart.dir/decoderplugin.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/decoderplugin.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/decoderplugin.cpp.obj: ../decoderplugin.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/SerialChart.dir/decoderplugin.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\decoderplugin.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\decoderplugin.cpp

CMakeFiles/SerialChart.dir/decoderplugin.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/decoderplugin.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\decoderplugin.cpp > CMakeFiles\SerialChart.dir\decoderplugin.cpp.i

CMakeFiles/SerialChart.dir/decoderplugin.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/decoderplugin.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\decoderplugin.cpp -o CMakeFiles\SerialChart.dir\decoderplugin.cpp.s

CMakeFiles/SerialChart.dir/displaybase.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/displaybase.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/displaybase.cpp.obj: ../displaybase.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/SerialChart.dir/displaybase.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\displaybase.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\displaybase.cpp

CMakeFiles/SerialChart.dir/displaybase.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/displaybase.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\displaybase.cpp > CMakeFiles\SerialChart.dir\displaybase.cpp.i

CMakeFiles/SerialChart.dir/displaybase.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/displaybase.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\displaybase.cpp -o CMakeFiles\SerialChart.dir\displaybase.cpp.s

CMakeFiles/SerialChart.dir/hiddevice.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/hiddevice.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/hiddevice.cpp.obj: ../hiddevice.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/SerialChart.dir/hiddevice.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\hiddevice.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\hiddevice.cpp

CMakeFiles/SerialChart.dir/hiddevice.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/hiddevice.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\hiddevice.cpp > CMakeFiles\SerialChart.dir\hiddevice.cpp.i

CMakeFiles/SerialChart.dir/hiddevice.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/hiddevice.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\hiddevice.cpp -o CMakeFiles\SerialChart.dir\hiddevice.cpp.s

CMakeFiles/SerialChart.dir/main.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/main.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/main.cpp.obj: ../main.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/SerialChart.dir/main.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\main.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\main.cpp

CMakeFiles/SerialChart.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/main.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\main.cpp > CMakeFiles\SerialChart.dir\main.cpp.i

CMakeFiles/SerialChart.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/main.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\main.cpp -o CMakeFiles\SerialChart.dir\main.cpp.s

CMakeFiles/SerialChart.dir/mainwindow.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/mainwindow.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/mainwindow.cpp.obj: ../mainwindow.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object CMakeFiles/SerialChart.dir/mainwindow.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\mainwindow.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\mainwindow.cpp

CMakeFiles/SerialChart.dir/mainwindow.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/mainwindow.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\mainwindow.cpp > CMakeFiles\SerialChart.dir\mainwindow.cpp.i

CMakeFiles/SerialChart.dir/mainwindow.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/mainwindow.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\mainwindow.cpp -o CMakeFiles\SerialChart.dir\mainwindow.cpp.s

CMakeFiles/SerialChart.dir/plugin.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/plugin.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/plugin.cpp.obj: ../plugin.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object CMakeFiles/SerialChart.dir/plugin.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\plugin.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\plugin.cpp

CMakeFiles/SerialChart.dir/plugin.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/plugin.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\plugin.cpp > CMakeFiles\SerialChart.dir\plugin.cpp.i

CMakeFiles/SerialChart.dir/plugin.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/plugin.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\plugin.cpp -o CMakeFiles\SerialChart.dir\plugin.cpp.s

CMakeFiles/SerialChart.dir/portbase.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/portbase.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/portbase.cpp.obj: ../portbase.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object CMakeFiles/SerialChart.dir/portbase.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\portbase.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\portbase.cpp

CMakeFiles/SerialChart.dir/portbase.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/portbase.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\portbase.cpp > CMakeFiles\SerialChart.dir\portbase.cpp.i

CMakeFiles/SerialChart.dir/portbase.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/portbase.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\portbase.cpp -o CMakeFiles\SerialChart.dir\portbase.cpp.s

CMakeFiles/SerialChart.dir/porthid.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/porthid.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/porthid.cpp.obj: ../porthid.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object CMakeFiles/SerialChart.dir/porthid.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\porthid.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\porthid.cpp

CMakeFiles/SerialChart.dir/porthid.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/porthid.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\porthid.cpp > CMakeFiles\SerialChart.dir\porthid.cpp.i

CMakeFiles/SerialChart.dir/porthid.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/porthid.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\porthid.cpp -o CMakeFiles\SerialChart.dir\porthid.cpp.s

CMakeFiles/SerialChart.dir/portrs232.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/portrs232.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/portrs232.cpp.obj: ../portrs232.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object CMakeFiles/SerialChart.dir/portrs232.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\portrs232.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\portrs232.cpp

CMakeFiles/SerialChart.dir/portrs232.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/portrs232.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\portrs232.cpp > CMakeFiles\SerialChart.dir\portrs232.cpp.i

CMakeFiles/SerialChart.dir/portrs232.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/portrs232.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\portrs232.cpp -o CMakeFiles\SerialChart.dir\portrs232.cpp.s

CMakeFiles/SerialChart.dir/serialchartjs.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/serialchartjs.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/serialchartjs.cpp.obj: ../serialchartjs.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object CMakeFiles/SerialChart.dir/serialchartjs.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\serialchartjs.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\serialchartjs.cpp

CMakeFiles/SerialChart.dir/serialchartjs.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/serialchartjs.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\serialchartjs.cpp > CMakeFiles\SerialChart.dir\serialchartjs.cpp.i

CMakeFiles/SerialChart.dir/serialchartjs.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/serialchartjs.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\serialchartjs.cpp -o CMakeFiles\SerialChart.dir\serialchartjs.cpp.s

CMakeFiles/SerialChart.dir/qrc_resources.cpp.obj: CMakeFiles/SerialChart.dir/flags.make
CMakeFiles/SerialChart.dir/qrc_resources.cpp.obj: CMakeFiles/SerialChart.dir/includes_CXX.rsp
CMakeFiles/SerialChart.dir/qrc_resources.cpp.obj: qrc_resources.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object CMakeFiles/SerialChart.dir/qrc_resources.cpp.obj"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\SerialChart.dir\qrc_resources.cpp.obj -c C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\qrc_resources.cpp

CMakeFiles/SerialChart.dir/qrc_resources.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SerialChart.dir/qrc_resources.cpp.i"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\qrc_resources.cpp > CMakeFiles\SerialChart.dir\qrc_resources.cpp.i

CMakeFiles/SerialChart.dir/qrc_resources.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SerialChart.dir/qrc_resources.cpp.s"
	D:\Libraries\Qt\_Legacy\5.5.1\Tools\mingw492_32\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\qrc_resources.cpp -o CMakeFiles\SerialChart.dir\qrc_resources.cpp.s

# Object files for target SerialChart
SerialChart_OBJECTS = \
"CMakeFiles/SerialChart.dir/SerialChart_autogen/mocs_compilation.cpp.obj" \
"CMakeFiles/SerialChart.dir/chart.cpp.obj" \
"CMakeFiles/SerialChart.dir/configuration.cpp.obj" \
"CMakeFiles/SerialChart.dir/decoderbase.cpp.obj" \
"CMakeFiles/SerialChart.dir/decoderbin.cpp.obj" \
"CMakeFiles/SerialChart.dir/decodercsv.cpp.obj" \
"CMakeFiles/SerialChart.dir/decoderhdlc.cpp.obj" \
"CMakeFiles/SerialChart.dir/decoderplugin.cpp.obj" \
"CMakeFiles/SerialChart.dir/displaybase.cpp.obj" \
"CMakeFiles/SerialChart.dir/hiddevice.cpp.obj" \
"CMakeFiles/SerialChart.dir/main.cpp.obj" \
"CMakeFiles/SerialChart.dir/mainwindow.cpp.obj" \
"CMakeFiles/SerialChart.dir/plugin.cpp.obj" \
"CMakeFiles/SerialChart.dir/portbase.cpp.obj" \
"CMakeFiles/SerialChart.dir/porthid.cpp.obj" \
"CMakeFiles/SerialChart.dir/portrs232.cpp.obj" \
"CMakeFiles/SerialChart.dir/serialchartjs.cpp.obj" \
"CMakeFiles/SerialChart.dir/qrc_resources.cpp.obj"

# External object files for target SerialChart
SerialChart_EXTERNAL_OBJECTS =

SerialChart.exe: CMakeFiles/SerialChart.dir/SerialChart_autogen/mocs_compilation.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/chart.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/configuration.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/decoderbase.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/decoderbin.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/decodercsv.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/decoderhdlc.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/decoderplugin.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/displaybase.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/hiddevice.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/main.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/mainwindow.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/plugin.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/portbase.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/porthid.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/portrs232.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/serialchartjs.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/qrc_resources.cpp.obj
SerialChart.exe: CMakeFiles/SerialChart.dir/build.make
SerialChart.exe: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/libQt5SerialPort.a
SerialChart.exe: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/libQt5WebKitWidgets.a
SerialChart.exe: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/libQt5Widgets.a
SerialChart.exe: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/libQt5WebKit.a
SerialChart.exe: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/libQt5Gui.a
SerialChart.exe: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/libQt5Network.a
SerialChart.exe: D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/libQt5Core.a
SerialChart.exe: CMakeFiles/SerialChart.dir/linklibs.rsp
SerialChart.exe: CMakeFiles/SerialChart.dir/objects1.rsp
SerialChart.exe: CMakeFiles/SerialChart.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Linking CXX executable SerialChart.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\SerialChart.dir\link.txt --verbose=$(VERBOSE)
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E make_directory C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/plugins/platforms/
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E copy D:\Libraries\Qt\_Legacy\5.5.1\5.5\mingw492_32\lib\cmake/../../plugins/platforms/qwindows.dll C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt/plugins/platforms/
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E copy D:\Libraries\Qt\_Legacy\5.5.1\5.5\mingw492_32\lib\cmake/../../bin/Qt5Core.dll C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E copy D:\Libraries\Qt\_Legacy\5.5.1\5.5\mingw492_32\lib\cmake/../../bin/Qt5Gui.dll C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E copy D:\Libraries\Qt\_Legacy\5.5.1\5.5\mingw492_32\lib\cmake/../../bin/Qt5Widgets.dll C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E copy D:\Libraries\Qt\_Legacy\5.5.1\5.5\mingw492_32\lib\cmake/../../bin/Qt5SerialPort.dll C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E copy D:\Libraries\Qt\_Legacy\5.5.1\5.5\mingw492_32\lib\cmake/../../bin/Qt5WebKit.dll C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt
	"D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E copy D:\Libraries\Qt\_Legacy\5.5.1\5.5\mingw492_32\lib\cmake/../../bin/Qt5WebKitWidgets.dll C:/Users/<USER>/Desktop/SerialChart/cmake-build-release-mingw-qt

# Rule to build all files generated by this target.
CMakeFiles/SerialChart.dir/build: SerialChart.exe

.PHONY : CMakeFiles/SerialChart.dir/build

CMakeFiles/SerialChart.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\SerialChart.dir\cmake_clean.cmake
.PHONY : CMakeFiles/SerialChart.dir/clean

CMakeFiles/SerialChart.dir/depend: qrc_resources.cpp
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\Users\<USER>\Desktop\SerialChart C:\Users\<USER>\Desktop\SerialChart C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles\SerialChart.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/SerialChart.dir/depend

