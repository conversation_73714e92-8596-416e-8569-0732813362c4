# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.17

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe"

# The command to remove a file.
RM = "D:\Program Files\JetBrains\CLion\CLion 2020.3.3\bin\cmake\win\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Desktop\SerialChart

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/SerialChart.dir/all

.PHONY : all

# The main recursive "preinstall" target.
preinstall:

.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/SerialChart.dir/clean
clean: CMakeFiles/SerialChart_autogen.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/SerialChart.dir

# All Build rule for target.
CMakeFiles/SerialChart.dir/all: CMakeFiles/SerialChart_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20 "Built target SerialChart"
.PHONY : CMakeFiles/SerialChart.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/SerialChart.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/SerialChart.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles 0
.PHONY : CMakeFiles/SerialChart.dir/rule

# Convenience name for target.
SerialChart: CMakeFiles/SerialChart.dir/rule

.PHONY : SerialChart

# clean rule for target.
CMakeFiles/SerialChart.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart.dir\build.make CMakeFiles/SerialChart.dir/clean
.PHONY : CMakeFiles/SerialChart.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/SerialChart_autogen.dir

# All Build rule for target.
CMakeFiles/SerialChart_autogen.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart_autogen.dir\build.make CMakeFiles/SerialChart_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart_autogen.dir\build.make CMakeFiles/SerialChart_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles --progress-num=21,22 "Built target SerialChart_autogen"
.PHONY : CMakeFiles/SerialChart_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/SerialChart_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/SerialChart_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Desktop\SerialChart\cmake-build-release-mingw-qt\CMakeFiles 0
.PHONY : CMakeFiles/SerialChart_autogen.dir/rule

# Convenience name for target.
SerialChart_autogen: CMakeFiles/SerialChart_autogen.dir/rule

.PHONY : SerialChart_autogen

# clean rule for target.
CMakeFiles/SerialChart_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SerialChart_autogen.dir\build.make CMakeFiles/SerialChart_autogen.dir/clean
.PHONY : CMakeFiles/SerialChart_autogen.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

