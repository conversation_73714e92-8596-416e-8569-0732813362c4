# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.17

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "../CMakeLists.txt"
  "CMakeFiles/3.17.5/CMakeCCompiler.cmake"
  "CMakeFiles/3.17.5/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.17.5/CMakeRCCompiler.cmake"
  "CMakeFiles/3.17.5/CMakeSystem.cmake"
  "../resources.qrc"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5/Qt5Config.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5/Qt5ConfigVersion.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Core/Qt5CoreConfig.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Core/Qt5CoreMacros.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5Gui_QDDSPlugin.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5Gui_QJp2Plugin.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5Gui_QMngPlugin.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Network/Qt5NetworkConfig.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Network/Qt5NetworkConfigVersion.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Network/Qt5Network_QGenericEnginePlugin.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Network/Qt5Network_QNativeWifiEnginePlugin.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5SerialPort/Qt5SerialPortConfig.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5SerialPort/Qt5SerialPortConfigVersion.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5WebKit/Qt5WebKitConfig.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5WebKit/Qt5WebKitConfigVersion.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5WebKitWidgets/Qt5WebKitWidgetsConfig.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5WebKitWidgets/Qt5WebKitWidgetsConfigVersion.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake"
  "D:/Libraries/Qt/_Legacy/5.5.1/5.5/mingw492_32/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/CMakeCInformation.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/CMakeCXXInformation.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/CMakeCommonLanguageInclude.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/CMakeExtraGeneratorDetermineCompilerMacrosAndIncludeDirs.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/CMakeFindCodeBlocks.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/CMakeGenericSystem.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/CMakeInitializeConfigs.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/CMakeLanguageInformation.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/CMakeParseArguments.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/CMakeRCInformation.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/CMakeSystemSpecificInformation.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/CMakeSystemSpecificInitialize.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/Compiler/GNU-C.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/Compiler/GNU-CXX.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/Compiler/GNU.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/Internal/CMakeCheckCompilerFlag.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/Platform/Windows-GNU-C-ABI.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/Platform/Windows-GNU-C.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/Platform/Windows-GNU-CXX-ABI.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/Platform/Windows-GNU-CXX.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/Platform/Windows-GNU.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/Platform/Windows-windres.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/Platform/Windows.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/Platform/WindowsPaths.cmake"
  "D:/Program Files/JetBrains/CLion/CLion 2020.3.3/bin/cmake/win/share/cmake-3.17/Modules/ProcessorCount.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "resources.qrc.depends"
  "CMakeFiles/SerialChart_autogen.dir/AutogenInfo.json"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/SerialChart.dir/DependInfo.cmake"
  "CMakeFiles/SerialChart_autogen.dir/DependInfo.cmake"
  )
